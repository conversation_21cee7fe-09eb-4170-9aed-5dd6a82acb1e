<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TrustWatch - Watch Authenticity Classifier</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            padding: 40px;
            max-width: 600px;
            width: 100%;
            text-align: center;
        }

        .logo {
            font-size: 2.5em;
            font-weight: bold;
            color: #333;
            margin-bottom: 10px;
        }

        .subtitle {
            color: #666;
            margin-bottom: 30px;
            font-size: 1.1em;
        }

        .upload-area {
            border: 3px dashed #ddd;
            border-radius: 15px;
            padding: 40px 20px;
            margin: 30px 0;
            transition: all 0.3s ease;
            cursor: pointer;
            position: relative;
            overflow: hidden;
        }

        .upload-area:hover {
            border-color: #667eea;
            background-color: #f8f9ff;
        }

        .upload-area.dragover {
            border-color: #667eea;
            background-color: #f0f4ff;
            transform: scale(1.02);
        }

        .upload-icon {
            font-size: 3em;
            color: #ddd;
            margin-bottom: 20px;
        }

        .upload-text {
            color: #666;
            font-size: 1.1em;
            margin-bottom: 15px;
        }

        .file-input {
            display: none;
        }

        .upload-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 30px;
            border-radius: 25px;
            font-size: 1em;
            cursor: pointer;
            transition: transform 0.2s ease;
        }

        .upload-btn:hover {
            transform: translateY(-2px);
        }

        .preview-container {
            margin: 20px 0;
            display: none;
        }

        .preview-image {
            max-width: 100%;
            max-height: 300px;
            border-radius: 10px;
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
        }

        .results-container {
            margin-top: 30px;
            display: none;
        }

        .result-card {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 25px;
            margin: 15px 0;
        }

        .result-main {
            font-size: 1.5em;
            font-weight: bold;
            margin-bottom: 10px;
        }

        .result-genuine {
            color: #28a745;
        }

        .result-fake {
            color: #dc3545;
        }

        .result-not-watch {
            color: #ffc107;
        }

        .confidence {
            font-size: 1.2em;
            color: #666;
            margin-bottom: 20px;
        }

        .all-results {
            text-align: left;
        }

        .result-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid #eee;
        }

        .result-item:last-child {
            border-bottom: none;
        }

        .progress-bar {
            background: #e9ecef;
            border-radius: 10px;
            height: 8px;
            width: 100px;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            border-radius: 10px;
            transition: width 0.5s ease;
        }

        .loading {
            display: none;
            margin: 20px 0;
        }

        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #667eea;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto 15px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .error {
            color: #dc3545;
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            border-radius: 10px;
            padding: 15px;
            margin: 20px 0;
            display: none;
        }

        .new-analysis-btn {
            background: #6c757d;
            color: white;
            border: none;
            padding: 10px 25px;
            border-radius: 20px;
            font-size: 0.9em;
            cursor: pointer;
            margin-top: 20px;
            transition: background 0.2s ease;
        }

        .new-analysis-btn:hover {
            background: #5a6268;
        }

        @media (max-width: 768px) {
            .container {
                padding: 20px;
                margin: 10px;
            }
            
            .logo {
                font-size: 2em;
            }
            
            .upload-area {
                padding: 30px 15px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="logo">🔍 TrustWatch</div>
        <div class="subtitle">AI-Powered Watch Authenticity Verification</div>
        
        <div class="upload-area" id="uploadArea">
            <div class="upload-icon">📷</div>
            <div class="upload-text">Drop your watch image here or click to browse</div>
            <button class="upload-btn" onclick="document.getElementById('fileInput').click()">
                Choose Image
            </button>
            <input type="file" id="fileInput" class="file-input" accept="image/*">
        </div>

        <div class="preview-container" id="previewContainer">
            <img id="previewImage" class="preview-image" alt="Preview">
        </div>

        <div class="loading" id="loading">
            <div class="spinner"></div>
            <div>Analyzing your watch image...</div>
        </div>

        <div class="error" id="error"></div>

        <div class="results-container" id="resultsContainer">
            <div class="result-card">
                <div class="result-main" id="resultMain"></div>
                <div class="confidence" id="confidence"></div>
                
                <div class="all-results">
                    <h4 style="margin-bottom: 15px; color: #333;">Detailed Analysis:</h4>
                    <div id="allResults"></div>
                </div>
                
                <button class="new-analysis-btn" onclick="resetAnalysis()">
                    Analyze Another Image
                </button>
            </div>
        </div>
    </div>

    <!-- TensorFlow.js -->
    <script src="https://cdn.jsdelivr.net/npm/@tensorflow/tfjs@latest"></script>
    <script src="https://cdn.jsdelivr.net/npm/@teachablemachine/image@0.8/dist/teachablemachine-image.min.js"></script>

    <script>
        const uploadArea = document.getElementById('uploadArea');
        const fileInput = document.getElementById('fileInput');
        const previewContainer = document.getElementById('previewContainer');
        const previewImage = document.getElementById('previewImage');
        const loading = document.getElementById('loading');
        const error = document.getElementById('error');
        const resultsContainer = document.getElementById('resultsContainer');
        const resultMain = document.getElementById('resultMain');
        const confidence = document.getElementById('confidence');
        const allResults = document.getElementById('allResults');

        // Model variables
        let model = null;
        let modelInfo = null;
        let isModelLoading = false;

        // Load model on page load
        async function loadModel() {
            if (isModelLoading || model) return;

            isModelLoading = true;
            console.log('Loading TensorFlow.js model...');

            try {
                // First get model info
                const infoResponse = await fetch('/api/model-info');
                const infoData = await infoResponse.json();

                if (infoData.success) {
                    modelInfo = infoData;
                    console.log('Model info loaded:', modelInfo);
                } else {
                    throw new Error('Failed to load model info');
                }

                // Load the model using Teachable Machine library with public endpoints
                const modelURL = '/api/model/model.json';
                const metadataURL = '/api/model/metadata.json';

                console.log('Loading model from public endpoints...');
                model = await tmImage.load(modelURL, metadataURL);
                console.log('Model loaded successfully');

                return true;
            } catch (error) {
                console.error('Error loading model:', error);
                showError('Failed to load AI model. Please refresh the page and try again.');
                return false;
            } finally {
                isModelLoading = false;
            }
        }

        // Initialize model loading when page loads
        window.addEventListener('load', () => {
            loadModel();
        });

        // Drag and drop functionality
        uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadArea.classList.add('dragover');
        });

        uploadArea.addEventListener('dragleave', () => {
            uploadArea.classList.remove('dragover');
        });

        uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                handleFile(files[0]);
            }
        });

        // File input change
        fileInput.addEventListener('change', (e) => {
            if (e.target.files.length > 0) {
                handleFile(e.target.files[0]);
            }
        });

        // Click to upload
        uploadArea.addEventListener('click', () => {
            fileInput.click();
        });

        function handleFile(file) {
            // Validate file type
            if (!file.type.startsWith('image/')) {
                showError('Please select a valid image file.');
                return;
            }

            // Validate file size (10MB limit)
            if (file.size > 10 * 1024 * 1024) {
                showError('File size must be less than 10MB.');
                return;
            }

            // Show preview
            const reader = new FileReader();
            reader.onload = (e) => {
                previewImage.src = e.target.result;
                previewContainer.style.display = 'block';
                uploadArea.style.display = 'none';
            };
            reader.readAsDataURL(file);

            // Upload and classify
            classifyImage(file);
        }

        async function classifyImage(file) {
            hideError();
            loading.style.display = 'block';
            resultsContainer.style.display = 'none';

            try {
                // Ensure model is loaded
                if (!model) {
                    showError('AI model is still loading. Please wait a moment and try again.');
                    loading.style.display = 'none';
                    return;
                }

                console.log('Classifying image...');

                // Create image element for processing
                const img = new Image();
                img.onload = async () => {
                    try {
                        // Use teachable machine to predict
                        const predictions = await model.predict(img);

                        // Process results
                        const results = predictions.map(prediction => ({
                            label: prediction.className,
                            confidence: Math.round(prediction.probability * 100 * 100) / 100
                        }));

                        // Sort by confidence (highest first)
                        results.sort((a, b) => b.confidence - a.confidence);

                        // Get the top prediction
                        const topPrediction = results[0];

                        console.log('Classification results:', results);

                        // Display results
                        const data = {
                            success: true,
                            prediction: topPrediction.label,
                            confidence: topPrediction.confidence,
                            allResults: results,
                            timestamp: new Date().toISOString()
                        };

                        displayResults(data);

                    } catch (error) {
                        console.error('Error during prediction:', error);
                        showError('Error analyzing the image. Please try again.');
                    } finally {
                        loading.style.display = 'none';
                    }
                };

                img.onerror = () => {
                    showError('Error loading the image. Please try a different image.');
                    loading.style.display = 'none';
                };

                // Load image from file
                const reader = new FileReader();
                reader.onload = (e) => {
                    img.src = e.target.result;
                };
                reader.readAsDataURL(file);

            } catch (error) {
                console.error('Error:', error);
                showError('Error processing the image. Please try again.');
                loading.style.display = 'none';
            }
        }

        function displayResults(data) {
            // Main result
            const prediction = data.prediction;
            resultMain.textContent = prediction;
            resultMain.className = 'result-main';
            
            if (prediction === 'Genuine') {
                resultMain.classList.add('result-genuine');
            } else if (prediction === 'Fake') {
                resultMain.classList.add('result-fake');
            } else {
                resultMain.classList.add('result-not-watch');
            }

            confidence.textContent = `Confidence: ${data.confidence}%`;

            // All results
            allResults.innerHTML = '';
            data.allResults.forEach(result => {
                const item = document.createElement('div');
                item.className = 'result-item';
                
                const progressBar = document.createElement('div');
                progressBar.className = 'progress-bar';
                
                const progressFill = document.createElement('div');
                progressFill.className = 'progress-fill';
                progressFill.style.width = `${result.confidence}%`;
                
                // Color based on label
                if (result.label === 'Genuine') {
                    progressFill.style.backgroundColor = '#28a745';
                } else if (result.label === 'Fake') {
                    progressFill.style.backgroundColor = '#dc3545';
                } else {
                    progressFill.style.backgroundColor = '#ffc107';
                }
                
                progressBar.appendChild(progressFill);
                
                item.innerHTML = `
                    <span>${result.label}</span>
                    <div style="display: flex; align-items: center; gap: 10px;">
                        <span>${result.confidence}%</span>
                    </div>
                `;
                
                item.appendChild(progressBar);
                allResults.appendChild(item);
            });

            resultsContainer.style.display = 'block';
        }

        function showError(message) {
            error.textContent = message;
            error.style.display = 'block';
        }

        function hideError() {
            error.style.display = 'none';
        }

        function resetAnalysis() {
            uploadArea.style.display = 'block';
            previewContainer.style.display = 'none';
            resultsContainer.style.display = 'none';
            loading.style.display = 'none';
            hideError();
            fileInput.value = '';
        }
    </script>
</body>
</html>
