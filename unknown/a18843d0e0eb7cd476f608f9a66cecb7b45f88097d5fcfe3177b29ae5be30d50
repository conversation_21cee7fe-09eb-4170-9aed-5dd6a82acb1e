{"name": "helmet", "description": "help secure Express/Connect apps with various HTTP headers", "version": "6.2.0", "author": "<PERSON> <<EMAIL>> (https://evilpacket.net)", "contributors": ["<PERSON> <<EMAIL>> (https://evanhahn.com)"], "homepage": "https://helmetjs.github.io/", "bugs": {"url": "https://github.com/helmetjs/helmet/issues", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "git://github.com/helmetjs/helmet.git"}, "license": "MIT", "keywords": ["express", "security", "headers", "backend"], "engines": {"node": ">=14.0.0"}, "exports": {"import": "./index.mjs", "require": "./index.cjs"}, "main": "./index.cjs", "types": "./index.d.cts"}