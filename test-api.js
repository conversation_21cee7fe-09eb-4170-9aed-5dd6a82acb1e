#!/usr/bin/env node

/**
 * TrustWatch API Test Script
 * 
 * This script tests the TrustWatch API endpoints to ensure they're working correctly.
 * Run this script after starting the server to verify the integration.
 */

const http = require('http');
const fs = require('fs');
const path = require('path');

const BASE_URL = 'http://localhost:3000';

// ANSI color codes for console output
const colors = {
    green: '\x1b[32m',
    red: '\x1b[31m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    reset: '\x1b[0m',
    bold: '\x1b[1m'
};

function log(message, color = 'reset') {
    console.log(`${colors[color]}${message}${colors.reset}`);
}

function makeRequest(options, data = null) {
    return new Promise((resolve, reject) => {
        const req = http.request(options, (res) => {
            let body = '';
            res.on('data', (chunk) => {
                body += chunk;
            });
            res.on('end', () => {
                try {
                    const jsonBody = JSON.parse(body);
                    resolve({ status: res.statusCode, data: jsonBody, headers: res.headers });
                } catch (e) {
                    resolve({ status: res.statusCode, data: body, headers: res.headers });
                }
            });
        });

        req.on('error', (err) => {
            reject(err);
        });

        if (data) {
            req.write(data);
        }
        req.end();
    });
}

async function testModelInfo() {
    log('\n🔍 Testing Model Info API...', 'blue');
    
    try {
        const options = {
            hostname: 'localhost',
            port: 3000,
            path: '/api/model-info',
            method: 'GET',
            headers: {
                'Content-Type': 'application/json'
            }
        };

        const response = await makeRequest(options);
        
        if (response.status === 200 && response.data.success) {
            log('✅ Model Info API: SUCCESS', 'green');
            log(`   Model Name: ${response.data.modelName}`, 'yellow');
            log(`   Labels: ${response.data.labels.join(', ')}`, 'yellow');
            log(`   Image Size: ${response.data.imageSize}x${response.data.imageSize}`, 'yellow');
            return true;
        } else {
            log('❌ Model Info API: FAILED', 'red');
            log(`   Status: ${response.status}`, 'red');
            log(`   Response: ${JSON.stringify(response.data, null, 2)}`, 'red');
            return false;
        }
    } catch (error) {
        log('❌ Model Info API: ERROR', 'red');
        log(`   Error: ${error.message}`, 'red');
        return false;
    }
}

async function testProtectedFiles() {
    log('\n🔒 Testing Protected Files Access...', 'blue');
    
    const files = ['model.json', 'metadata.json', 'weights.bin'];
    let allSuccess = true;

    for (const file of files) {
        try {
            const options = {
                hostname: 'localhost',
                port: 3000,
                path: `/api/protected/${file}`,
                method: 'GET',
                headers: {
                    'x-secret-header': 'mySecretValue'
                }
            };

            const response = await makeRequest(options);
            
            if (response.status === 200) {
                log(`✅ Protected file ${file}: SUCCESS`, 'green');
            } else {
                log(`❌ Protected file ${file}: FAILED (Status: ${response.status})`, 'red');
                allSuccess = false;
            }
        } catch (error) {
            log(`❌ Protected file ${file}: ERROR - ${error.message}`, 'red');
            allSuccess = false;
        }
    }

    return allSuccess;
}

async function testProtectedFilesWithoutAuth() {
    log('\n🚫 Testing Protected Files Without Authentication...', 'blue');
    
    try {
        const options = {
            hostname: 'localhost',
            port: 3000,
            path: '/api/protected/model.json',
            method: 'GET'
            // No authentication header
        };

        const response = await makeRequest(options);
        
        if (response.status === 403) {
            log('✅ Protected files security: SUCCESS (Access denied without auth)', 'green');
            return true;
        } else {
            log('❌ Protected files security: FAILED (Should deny access without auth)', 'red');
            log(`   Status: ${response.status}`, 'red');
            return false;
        }
    } catch (error) {
        log('❌ Protected files security test: ERROR', 'red');
        log(`   Error: ${error.message}`, 'red');
        return false;
    }
}

async function testFrontendAccess() {
    log('\n🌐 Testing Frontend Access...', 'blue');
    
    try {
        const options = {
            hostname: 'localhost',
            port: 3000,
            path: '/watch-classifier.html',
            method: 'GET'
        };

        const response = await makeRequest(options);
        
        if (response.status === 200) {
            log('✅ Frontend access: SUCCESS', 'green');
            log('   TrustWatch web interface is accessible', 'yellow');
            return true;
        } else {
            log('❌ Frontend access: FAILED', 'red');
            log(`   Status: ${response.status}`, 'red');
            return false;
        }
    } catch (error) {
        log('❌ Frontend access: ERROR', 'red');
        log(`   Error: ${error.message}`, 'red');
        return false;
    }
}

async function runAllTests() {
    log('🚀 TrustWatch API Integration Test Suite', 'bold');
    log('==========================================', 'bold');
    
    const results = [];
    
    // Test server connectivity first
    log('\n📡 Checking server connectivity...', 'blue');
    try {
        const options = {
            hostname: 'localhost',
            port: 3000,
            path: '/',
            method: 'GET'
        };
        await makeRequest(options);
        log('✅ Server is running and accessible', 'green');
    } catch (error) {
        log('❌ Cannot connect to server. Make sure the server is running on port 3000.', 'red');
        log('   Run: npm start', 'yellow');
        process.exit(1);
    }

    // Run all tests
    results.push(await testModelInfo());
    results.push(await testProtectedFiles());
    results.push(await testProtectedFilesWithoutAuth());
    results.push(await testFrontendAccess());

    // Summary
    log('\n📊 Test Results Summary', 'bold');
    log('======================', 'bold');
    
    const passed = results.filter(r => r).length;
    const total = results.length;
    
    if (passed === total) {
        log(`🎉 All tests passed! (${passed}/${total})`, 'green');
        log('\n✨ TrustWatch API is ready to use!', 'green');
        log(`🌐 Open: ${BASE_URL}/watch-classifier.html`, 'blue');
    } else {
        log(`⚠️  Some tests failed. (${passed}/${total} passed)`, 'yellow');
        log('\n🔧 Please check the server configuration and try again.', 'yellow');
    }
    
    log('\n📚 API Endpoints:', 'bold');
    log(`   GET  ${BASE_URL}/api/model-info`, 'blue');
    log(`   POST ${BASE_URL}/api/upload-image`, 'blue');
    log(`   GET  ${BASE_URL}/api/protected/:filename`, 'blue');
    log(`   GET  ${BASE_URL}/watch-classifier.html`, 'blue');
}

// Run tests if this script is executed directly
if (require.main === module) {
    runAllTests().catch(console.error);
}

module.exports = { runAllTests, testModelInfo, testProtectedFiles };
