{"name": "trustwatch-api", "version": "1.0.0", "description": "TrustWatch - AI-powered watch authenticity verification API", "main": "server.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "start": "node server.js", "dev": "node server.js"}, "keywords": ["watch", "authenticity", "AI", "classification", "API"], "author": "Your Name", "license": "ISC", "engines": {"node": ">=14.0.0"}, "dependencies": {"express": "^4.19.2", "helmet": "^6.0.0", "express-rate-limit": "^6.7.0", "multer": "^1.4.5-lts.1", "cors": "^2.8.5"}}