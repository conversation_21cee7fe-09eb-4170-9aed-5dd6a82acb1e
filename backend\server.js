const express = require('express');
const fs = require('fs');
const path = require('path');
const multer = require('multer');
const cors = require('cors');

const helmet = require('helmet');
const rateLimit = require('express-rate-limit');

const app = express();
const PORT = process.env.PORT || 3000;

// Configure multer for file uploads (for future use if needed)
const storage = multer.memoryStorage();
const upload = multer({
    storage: storage,
    limits: {
        fileSize: 10 * 1024 * 1024, // 10MB limit
    },
    fileFilter: (req, file, cb) => {
        // Accept only image files
        if (file.mimetype.startsWith('image/')) {
            cb(null, true);
        } else {
            cb(new Error('Only image files are allowed!'), false);
        }
    }
});

// Nascondi l'header X-Powered-By
app.disable('x-powered-by');

// Enable CORS for frontend communication
app.use(cors({
    origin: ['http://localhost:3000', 'http://127.0.0.1:3000'],
    credentials: true
}));

// Parse JSON bodies
app.use(express.json());

// Use Helmet to set various HTTP headers for security
//app.use(helmet());

// Imposta manualmente gli header di sicurezza, escludendo X-Frame-Options, Content-Security-Policy
app.use((req, res, next) => {
    res.setHeader('X-Content-Type-Options', 'nosniff');
    res.setHeader('X-XSS-Protection', '1; mode=block');
    res.setHeader('Strict-Transport-Security', 'max-age=31536000; includeSubDomains');
    res.setHeader('Referrer-Policy', 'no-referrer');
    //res.setHeader('Content-Security-Policy', "default-src 'self'");
    next();
});

// Rate limiting to prevent brute force attacks
const apiLimiter = rateLimit({
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 100 // limit each IP to 100 requests per windowMs
});
app.use('/api/', apiLimiter);

// Middleware to serve static files
app.use(express.static(path.join(__dirname, '../frontend')));

// API endpoint to get model metadata
app.get('/api/model-info', (req, res) => {
    try {
        const metadataPath = path.join(__dirname, 'protected', 'metadata.json');
        const metadata = JSON.parse(fs.readFileSync(metadataPath, 'utf8'));

        res.json({
            success: true,
            labels: metadata.labels,
            imageSize: metadata.imageSize,
            modelName: metadata.modelName
        });
    } catch (error) {
        console.error('Error reading model metadata:', error);
        res.status(500).json({
            success: false,
            error: 'Could not load model information'
        });
    }
});

// Public model endpoints for frontend (with CORS headers)
app.get('/api/model/:filename', (req, res) => {
    const { filename } = req.params;

    // Only allow specific model files
    const allowedFiles = ['model.json', 'metadata.json', 'weights.bin'];
    if (!allowedFiles.includes(filename)) {
        return res.status(404).send('File not found');
    }

    const filePath = path.join(__dirname, 'protected', filename);

    fs.access(filePath, fs.constants.R_OK, (err) => {
        if (err) {
            return res.status(404).send('File not found');
        }

        // Set appropriate headers for model files
        if (filename.endsWith('.json')) {
            res.setHeader('Content-Type', 'application/json');
        } else if (filename.endsWith('.bin')) {
            res.setHeader('Content-Type', 'application/octet-stream');
        }

        // Add CORS headers for model loading
        res.setHeader('Access-Control-Allow-Origin', '*');
        res.setHeader('Access-Control-Allow-Methods', 'GET');
        res.setHeader('Access-Control-Allow-Headers', 'Content-Type');

        res.sendFile(filePath);
    });
});

// API endpoint for image upload and processing (for future server-side processing if needed)
app.post('/api/upload-image', upload.single('image'), (req, res) => {
    try {
        if (!req.file) {
            return res.status(400).json({
                success: false,
                error: 'No image file provided'
            });
        }

        // For now, just return success - processing will be done client-side
        res.json({
            success: true,
            message: 'Image uploaded successfully',
            filename: req.file.originalname,
            size: req.file.size,
            mimetype: req.file.mimetype
        });
    } catch (error) {
        console.error('Error uploading image:', error);
        res.status(500).json({
            success: false,
            error: 'Error uploading image'
        });
    }
});

// Middleware per verificare l'header personalizzato
app.use('/api/protected/*', (req, res, next) => {
    const secretHeader = req.headers['x-secret-header'];
    if (secretHeader !== 'mySecretValue') {
        return res.status(403).send('Forbidden');
    }
    next();
});

// API endpoint to serve protected files
app.get('/api/protected/:filename', (req, res) => {
    const { filename } = req.params;
    const filePath = path.join(__dirname, 'protected', filename);

    fs.access(filePath, fs.constants.R_OK, (err) => {
        if (err) {
            return res.status(404).send('File not found');
        }
        res.sendFile(filePath);
    });
});

app.listen(PORT, () => {
    console.log(`Server is running on http://localhost:${PORT}`);
    console.log('TrustWatch API endpoints:');
    console.log('- GET /api/model-info - Get model information');
    console.log('- GET /api/model/:filename - Public model files (model.json, metadata.json, weights.bin)');
    console.log('- POST /api/upload-image - Upload image for processing');
    console.log('- GET /api/protected/:filename - Access protected model files (with auth)');
    console.log('- Frontend available at: http://localhost:' + PORT + '/watch-classifier.html');
});
