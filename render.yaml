services:
  - type: web
    name: trustwatch-api
    env: node
    plan: starter
    buildCommand: cd backend && npm install
    startCommand: cd backend && npm start
    envVars:
      - key: NODE_ENV
        value: production
      - key: RAPIDAPI_PROXY_SECRET
        generateValue: true
      - key: ALLOWED_ORIGINS
        value: "https://rapidapi.com,https://rapidapi.p.rapidapi.com"
    healthCheckPath: /api/model-info
