openapi: 3.0.0
info:
  title: TrustWatch - Watch Authenticity Verification API
  description: |
    AI-powered watch authenticity verification service that analyzes watch images to determine if they are genuine, fake, or not a watch at all.
    
    ## Features
    - Real-time image classification using advanced AI
    - Three classification categories: Genuine, Fake, Not a watch
    - Confidence scores for each prediction
    - Support for multiple image formats (JPEG, PNG, WebP)
    - Maximum file size: 10MB
    
    ## How it works
    1. Upload a watch image using the classify endpoint
    2. Get model information and endpoints for client-side processing
    3. Use TensorFlow.js on the client side for real-time classification
    
    ## Use Cases
    - E-commerce platforms for product verification
    - Watch collectors and enthusiasts
    - Authentication services
    - Quality control in manufacturing
  version: 1.0.0
  contact:
    name: TrustWatch API Support
    email: <EMAIL>
  license:
    name: Commercial License
    url: https://trustwatch.com/license

servers:
  - url: https://your-render-app.onrender.com
    description: Production server

paths:
  /api/rapidapi/model-info:
    get:
      summary: Get Model Information
      description: Returns information about the AI model including supported labels, image size requirements, and service details.
      tags:
        - Model Information
      responses:
        '200':
          description: Model information retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  service:
                    type: string
                    example: "TrustWatch - Watch Authenticity Verification"
                  version:
                    type: string
                    example: "1.0.0"
                  labels:
                    type: array
                    items:
                      type: string
                    example: ["Genuine", "Fake", "Not a watch"]
                  imageSize:
                    type: integer
                    example: 224
                  modelName:
                    type: string
                    example: "Il mio modello image"
                  description:
                    type: string
                    example: "AI-powered watch authenticity verification service"
                  supportedFormats:
                    type: array
                    items:
                      type: string
                    example: ["image/jpeg", "image/png", "image/webp"]
                  maxFileSize:
                    type: string
                    example: "10MB"
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /api/rapidapi/classify-watch:
    post:
      summary: Classify Watch Image
      description: |
        Upload a watch image for authenticity verification. This endpoint returns model information and endpoints for client-side processing using TensorFlow.js.
        
        **Note**: This API uses client-side processing for optimal performance. The response includes model endpoints that you can use with TensorFlow.js to perform real-time classification.
      tags:
        - Image Classification
      requestBody:
        required: true
        content:
          multipart/form-data:
            schema:
              type: object
              properties:
                image:
                  type: string
                  format: binary
                  description: Watch image file (JPEG, PNG, WebP)
              required:
                - image
      responses:
        '200':
          description: Image processed successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  message:
                    type: string
                    example: "Image received successfully"
                  imageInfo:
                    type: object
                    properties:
                      filename:
                        type: string
                        example: "watch.jpg"
                      size:
                        type: integer
                        example: 1024000
                      mimetype:
                        type: string
                        example: "image/jpeg"
                  instructions:
                    type: object
                    properties:
                      message:
                        type: string
                        example: "Use the client-side classification with the provided model endpoints"
                      modelEndpoints:
                        type: object
                        properties:
                          model:
                            type: string
                            example: "https://your-app.onrender.com/api/model/model.json"
                          metadata:
                            type: string
                            example: "https://your-app.onrender.com/api/model/metadata.json"
                          weights:
                            type: string
                            example: "https://your-app.onrender.com/api/model/weights.bin"
                  timestamp:
                    type: string
                    format: date-time
                    example: "2024-01-15T10:30:00.000Z"
        '400':
          description: Bad request - missing or invalid image
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: false
                  error:
                    type: string
                    example: "No image file provided. Please upload an image file."
                  code:
                    type: string
                    example: "MISSING_IMAGE"
        '401':
          description: Unauthorized - missing RapidAPI authentication
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /api/model/model.json:
    get:
      summary: Get TensorFlow.js Model
      description: Returns the TensorFlow.js model file for client-side processing
      tags:
        - Model Files
      responses:
        '200':
          description: Model file
          content:
            application/json:
              schema:
                type: object

  /api/model/metadata.json:
    get:
      summary: Get Model Metadata
      description: Returns the model metadata file for client-side processing
      tags:
        - Model Files
      responses:
        '200':
          description: Metadata file
          content:
            application/json:
              schema:
                type: object

  /api/model/weights.bin:
    get:
      summary: Get Model Weights
      description: Returns the model weights file for client-side processing
      tags:
        - Model Files
      responses:
        '200':
          description: Weights file
          content:
            application/octet-stream:
              schema:
                type: string
                format: binary

components:
  schemas:
    Error:
      type: object
      properties:
        success:
          type: boolean
          example: false
        error:
          type: string
          example: "Error message"
        code:
          type: string
          example: "ERROR_CODE"

  securitySchemes:
    RapidAPI:
      type: apiKey
      in: header
      name: X-RapidAPI-Key
      description: RapidAPI subscription key

security:
  - RapidAPI: []

tags:
  - name: Model Information
    description: Get information about the AI model
  - name: Image Classification
    description: Upload and classify watch images
  - name: Model Files
    description: Access model files for client-side processing
