# TrustWatch - AI-Powered Watch Authenticity Verification

TrustWatch is an AI-powered system that analyzes watch images to determine if they are genuine, fake, or not a watch at all. The system uses a trained TensorFlow.js model to provide real-time classification with confidence scores.

## Features

- **Real-time Image Classification**: Upload watch images and get instant results
- **Three Classification Categories**:
  - ✅ **Genuine**: Authentic watch
  - ❌ **Fake**: Counterfeit/replica watch  
  - ⚠️ **Not a watch**: Image doesn't contain a watch
- **Confidence Scores**: Get percentage confidence for each prediction
- **Modern Web Interface**: Clean, responsive design with drag-and-drop upload
- **Secure API**: Protected model files with authentication
- **Cross-platform**: Works on any device with a web browser

## Technology Stack

### Backend
- **Node.js** with Express.js
- **Multer** for file upload handling
- **CORS** for cross-origin requests
- **Rate limiting** for API protection
- **Security headers** with Helmet.js

### Frontend
- **TensorFlow.js** for client-side AI inference
- **Teachable Machine** integration for model loading
- **Modern HTML5/CSS3/JavaScript**
- **Responsive design** for mobile and desktop

### AI Model
- **TensorFlow.js** trained model
- **Image classification** with 224x224 input size
- **Three-class output**: Genuine, Fake, Not a watch

## Installation & Setup

### Prerequisites
- Node.js (v14 or higher)
- npm or yarn package manager

### 1. Install Dependencies
```bash
cd backend
npm install
```

### 2. Start the Server
```bash
npm start
```

The server will start on `http://localhost:3000`

### 3. Access the Application
Open your browser and navigate to:
```
http://localhost:3000/watch-classifier.html
```

## API Endpoints

### GET /api/model-info
Returns information about the AI model.

**Response:**
```json
{
  "success": true,
  "labels": ["Genuine", "Fake", "Not a watch"],
  "imageSize": 224,
  "modelName": "Il mio modello image"
}
```

### POST /api/upload-image
Upload an image file for processing (currently for future server-side processing).

**Request:** Multipart form data with `image` field
**Response:**
```json
{
  "success": true,
  "message": "Image uploaded successfully",
  "filename": "watch.jpg",
  "size": 1024000,
  "mimetype": "image/jpeg"
}
```

### GET /api/protected/:filename
Access protected model files (requires authentication header).

**Headers:** `x-secret-header: mySecretValue`

## Usage

### Web Interface
1. Open the TrustWatch web application
2. Drag and drop a watch image or click "Choose Image"
3. Wait for the AI model to load (first time only)
4. View the classification results with confidence scores
5. Click "Analyze Another Image" to test more images

### Supported Image Formats
- JPEG (.jpg, .jpeg)
- PNG (.png)
- WebP (.webp)
- GIF (.gif)
- Maximum file size: 10MB

## Model Information

The AI model was trained using Google's Teachable Machine platform and can classify images into three categories:

1. **Genuine Watches**: Authentic timepieces from legitimate manufacturers
2. **Fake Watches**: Counterfeit or replica watches
3. **Not a Watch**: Images that don't contain watches

### Model Specifications
- **Input Size**: 224x224 pixels
- **Model Type**: Image Classification (CNN)
- **Framework**: TensorFlow.js
- **Inference**: Client-side (browser-based)

## Security Features

- **Rate Limiting**: API requests are limited to prevent abuse
- **File Type Validation**: Only image files are accepted
- **File Size Limits**: Maximum 10MB per upload
- **Protected Model Files**: Model files require authentication
- **Security Headers**: CORS, XSS protection, content type validation
- **Input Sanitization**: All inputs are validated and sanitized

## Development

### Project Structure
```
├── backend/
│   ├── server.js          # Express server
│   ├── package.json       # Dependencies
│   └── protected/         # AI model files
│       ├── model.json     # TensorFlow.js model
│       ├── weights.bin    # Model weights
│       └── metadata.json  # Model metadata
├── frontend/
│   ├── watch-classifier.html  # Main web interface
│   ├── index.html         # Original interface
│   └── index1.html        # Alternative interface
└── README.md
```

### Adding New Features
1. **Server-side Processing**: Uncomment TensorFlow.js Node dependencies for server-side inference
2. **Database Integration**: Add database to store classification results
3. **User Authentication**: Implement user accounts and history
4. **Batch Processing**: Support multiple image uploads
5. **API Rate Limiting**: Customize rate limits per user/IP

## Troubleshooting

### Common Issues

**Model Loading Errors**
- Ensure the server is running on port 3000
- Check that model files exist in `backend/protected/`
- Verify the secret header is correctly set

**Image Upload Failures**
- Check file size (max 10MB)
- Ensure file is a valid image format
- Verify network connection

**Classification Errors**
- Wait for model to fully load before uploading
- Try refreshing the page
- Check browser console for error messages

### Browser Compatibility
- Chrome 60+
- Firefox 55+
- Safari 11+
- Edge 79+

## License

This project is for educational and demonstration purposes. The AI model and code are provided as-is.

## Support

For issues and questions, please check the troubleshooting section or review the browser console for error messages.
