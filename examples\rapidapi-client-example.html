<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TrustWatch API - Client Example</title>
    <script src="https://cdn.jsdelivr.net/npm/@tensorflow/tfjs@latest"></script>
    <script src="https://cdn.jsdelivr.net/npm/@teachablemachine/image@0.8/dist/teachablemachine-image.min.js"></script>
    <style>
        body { font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px; }
        .container { background: #f5f5f5; padding: 20px; border-radius: 10px; margin: 20px 0; }
        .result { background: #e8f5e8; padding: 15px; border-radius: 5px; margin: 10px 0; }
        .error { background: #ffe8e8; padding: 15px; border-radius: 5px; margin: 10px 0; }
        button { background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; }
        button:hover { background: #0056b3; }
        input[type="file"] { margin: 10px 0; }
        .api-key-input { width: 100%; padding: 10px; margin: 10px 0; border: 1px solid #ddd; border-radius: 5px; }
    </style>
</head>
<body>
    <h1>🔍 TrustWatch API - Client Example</h1>
    <p>This example shows how to use the TrustWatch API from RapidAPI to classify watch images.</p>
    
    <div class="container">
        <h3>Step 1: Configure API</h3>
        <input type="text" id="apiKey" class="api-key-input" placeholder="Enter your RapidAPI Key" />
        <input type="text" id="apiHost" class="api-key-input" placeholder="API Host (e.g., trustwatch-api.p.rapidapi.com)" />
        <input type="text" id="baseUrl" class="api-key-input" placeholder="Base URL (e.g., https://trustwatch-api.p.rapidapi.com)" />
    </div>

    <div class="container">
        <h3>Step 2: Upload Watch Image</h3>
        <input type="file" id="imageInput" accept="image/*" />
        <button onclick="classifyWatch()">Classify Watch</button>
    </div>

    <div id="results"></div>

    <script>
        let model = null;
        let modelEndpoints = null;

        async function classifyWatch() {
            const apiKey = document.getElementById('apiKey').value;
            const apiHost = document.getElementById('apiHost').value;
            const baseUrl = document.getElementById('baseUrl').value;
            const fileInput = document.getElementById('imageInput');
            const resultsDiv = document.getElementById('results');

            if (!apiKey || !apiHost || !baseUrl) {
                showError('Please fill in all API configuration fields');
                return;
            }

            if (!fileInput.files[0]) {
                showError('Please select an image file');
                return;
            }

            try {
                showResult('Uploading image to TrustWatch API...', 'info');

                // Step 1: Upload image to RapidAPI endpoint
                const formData = new FormData();
                formData.append('image', fileInput.files[0]);

                const uploadResponse = await fetch(`${baseUrl}/api/rapidapi/classify-watch`, {
                    method: 'POST',
                    headers: {
                        'X-RapidAPI-Key': apiKey,
                        'X-RapidAPI-Host': apiHost
                    },
                    body: formData
                });

                const uploadResult = await uploadResponse.json();

                if (!uploadResult.success) {
                    throw new Error(uploadResult.error || 'Upload failed');
                }

                showResult('Image uploaded successfully! Loading AI model...', 'info');

                // Step 2: Get model endpoints
                modelEndpoints = uploadResult.instructions.modelEndpoints;

                // Step 3: Load TensorFlow.js model
                if (!model) {
                    model = await tmImage.load(
                        modelEndpoints.model,
                        modelEndpoints.metadata
                    );
                    showResult('AI model loaded successfully! Classifying image...', 'info');
                }

                // Step 4: Classify the image
                const img = new Image();
                img.onload = async () => {
                    try {
                        const predictions = await model.predict(img);
                        
                        const results = predictions.map(prediction => ({
                            label: prediction.className,
                            confidence: Math.round(prediction.probability * 100 * 100) / 100
                        }));
                        
                        results.sort((a, b) => b.confidence - a.confidence);
                        
                        displayClassificationResults(results, uploadResult.imageInfo);
                        
                    } catch (error) {
                        showError('Classification failed: ' + error.message);
                    }
                };

                img.onerror = () => {
                    showError('Failed to load image for classification');
                };

                img.src = URL.createObjectURL(fileInput.files[0]);

            } catch (error) {
                showError('Error: ' + error.message);
            }
        }

        function displayClassificationResults(results, imageInfo) {
            const topResult = results[0];
            let resultClass = 'result';
            let emoji = '🔍';
            
            if (topResult.label === 'Genuine') {
                emoji = '✅';
            } else if (topResult.label === 'Fake') {
                emoji = '❌';
            } else {
                emoji = '⚠️';
            }

            const html = `
                <div class="${resultClass}">
                    <h3>${emoji} Classification Results</h3>
                    <p><strong>Image:</strong> ${imageInfo.filename} (${(imageInfo.size / 1024).toFixed(1)} KB)</p>
                    <p><strong>Prediction:</strong> ${topResult.label}</p>
                    <p><strong>Confidence:</strong> ${topResult.confidence}%</p>
                    
                    <h4>Detailed Results:</h4>
                    <ul>
                        ${results.map(r => `<li>${r.label}: ${r.confidence}%</li>`).join('')}
                    </ul>
                    
                    <p><em>Powered by TrustWatch API via RapidAPI</em></p>
                </div>
            `;

            document.getElementById('results').innerHTML = html;
        }

        function showResult(message, type = 'result') {
            const className = type === 'info' ? 'result' : type;
            document.getElementById('results').innerHTML = `<div class="${className}">${message}</div>`;
        }

        function showError(message) {
            document.getElementById('results').innerHTML = `<div class="error">❌ ${message}</div>`;
        }

        // Example API configuration (users should replace with their own)
        document.addEventListener('DOMContentLoaded', () => {
            document.getElementById('apiHost').value = 'trustwatch-api.p.rapidapi.com';
            document.getElementById('baseUrl').value = 'https://trustwatch-api.p.rapidapi.com';
        });
    </script>

    <div class="container">
        <h3>Integration Guide</h3>
        <p><strong>Step 1:</strong> Subscribe to TrustWatch API on RapidAPI</p>
        <p><strong>Step 2:</strong> Get your API key from RapidAPI dashboard</p>
        <p><strong>Step 3:</strong> Use the API endpoints to upload images and get model information</p>
        <p><strong>Step 4:</strong> Use TensorFlow.js on the client side for real-time classification</p>
        
        <h4>Supported Image Formats:</h4>
        <ul>
            <li>JPEG (.jpg, .jpeg)</li>
            <li>PNG (.png)</li>
            <li>WebP (.webp)</li>
        </ul>
        
        <h4>Classification Categories:</h4>
        <ul>
            <li>✅ <strong>Genuine:</strong> Authentic watch</li>
            <li>❌ <strong>Fake:</strong> Counterfeit/replica watch</li>
            <li>⚠️ <strong>Not a watch:</strong> Image doesn't contain a watch</li>
        </ul>
    </div>
</body>
</html>
