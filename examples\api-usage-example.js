/**
 * TrustWatch API Usage Examples
 * 
 * This file demonstrates how to integrate the TrustWatch API
 * into your own applications for watch authenticity verification.
 */

// Example 1: Basic API Integration (Node.js)
const http = require('http');
const fs = require('fs');
const FormData = require('form-data'); // npm install form-data

class TrustWatchAPI {
    constructor(baseUrl = 'http://localhost:3000') {
        this.baseUrl = baseUrl;
    }

    /**
     * Get model information
     */
    async getModelInfo() {
        return new Promise((resolve, reject) => {
            const options = {
                hostname: 'localhost',
                port: 3000,
                path: '/api/model-info',
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json'
                }
            };

            const req = http.request(options, (res) => {
                let body = '';
                res.on('data', (chunk) => body += chunk);
                res.on('end', () => {
                    try {
                        resolve(JSON.parse(body));
                    } catch (e) {
                        reject(e);
                    }
                });
            });

            req.on('error', reject);
            req.end();
        });
    }

    /**
     * Upload image for processing
     */
    async uploadImage(imagePath) {
        return new Promise((resolve, reject) => {
            const form = new FormData();
            form.append('image', fs.createReadStream(imagePath));

            const options = {
                hostname: 'localhost',
                port: 3000,
                path: '/api/upload-image',
                method: 'POST',
                headers: form.getHeaders()
            };

            const req = http.request(options, (res) => {
                let body = '';
                res.on('data', (chunk) => body += chunk);
                res.on('end', () => {
                    try {
                        resolve(JSON.parse(body));
                    } catch (e) {
                        reject(e);
                    }
                });
            });

            req.on('error', reject);
            form.pipe(req);
        });
    }
}

// Example 2: Frontend JavaScript Integration
const frontendExample = `
// Frontend JavaScript example for web applications

class TrustWatchClient {
    constructor(baseUrl = '') {
        this.baseUrl = baseUrl;
        this.model = null;
        this.modelInfo = null;
    }

    async loadModel() {
        try {
            // Get model info
            const response = await fetch(this.baseUrl + '/api/model-info');
            this.modelInfo = await response.json();
            
            if (!this.modelInfo.success) {
                throw new Error('Failed to load model info');
            }

            // Load TensorFlow.js model using Teachable Machine
            const modelURL = this.baseUrl + '/api/protected/model.json';
            const metadataURL = this.baseUrl + '/api/protected/metadata.json';
            
            this.model = await tmImage.load(modelURL, metadataURL);
            console.log('Model loaded successfully');
            return true;
        } catch (error) {
            console.error('Error loading model:', error);
            return false;
        }
    }

    async classifyImage(imageElement) {
        if (!this.model) {
            throw new Error('Model not loaded. Call loadModel() first.');
        }

        try {
            const predictions = await this.model.predict(imageElement);
            
            const results = predictions.map(prediction => ({
                label: prediction.className,
                confidence: Math.round(prediction.probability * 100 * 100) / 100
            }));
            
            results.sort((a, b) => b.confidence - a.confidence);
            
            return {
                success: true,
                prediction: results[0].label,
                confidence: results[0].confidence,
                allResults: results,
                timestamp: new Date().toISOString()
            };
        } catch (error) {
            throw new Error('Classification failed: ' + error.message);
        }
    }

    async uploadAndClassify(file) {
        try {
            // Upload file
            const formData = new FormData();
            formData.append('image', file);
            
            const uploadResponse = await fetch(this.baseUrl + '/api/upload-image', {
                method: 'POST',
                body: formData
            });
            
            const uploadResult = await uploadResponse.json();
            
            if (!uploadResult.success) {
                throw new Error('Upload failed: ' + uploadResult.error);
            }

            // Create image element for classification
            const img = new Image();
            const imageUrl = URL.createObjectURL(file);
            
            return new Promise((resolve, reject) => {
                img.onload = async () => {
                    try {
                        const result = await this.classifyImage(img);
                        URL.revokeObjectURL(imageUrl);
                        resolve(result);
                    } catch (error) {
                        URL.revokeObjectURL(imageUrl);
                        reject(error);
                    }
                };
                
                img.onerror = () => {
                    URL.revokeObjectURL(imageUrl);
                    reject(new Error('Failed to load image'));
                };
                
                img.src = imageUrl;
            });
        } catch (error) {
            throw new Error('Upload and classification failed: ' + error.message);
        }
    }
}

// Usage example:
async function example() {
    const client = new TrustWatchClient();
    
    // Load the model
    await client.loadModel();
    
    // Get file from input element
    const fileInput = document.getElementById('imageInput');
    const file = fileInput.files[0];
    
    if (file) {
        try {
            const result = await client.uploadAndClassify(file);
            console.log('Classification result:', result);
            
            // Display results
            document.getElementById('result').innerHTML = \`
                <h3>Result: \${result.prediction}</h3>
                <p>Confidence: \${result.confidence}%</p>
                <div>
                    \${result.allResults.map(r => 
                        \`<div>\${r.label}: \${r.confidence}%</div>\`
                    ).join('')}
                </div>
            \`;
        } catch (error) {
            console.error('Error:', error);
            document.getElementById('result').innerHTML = 
                '<p style="color: red;">Error: ' + error.message + '</p>';
        }
    }
}
`;

// Example 3: React Component Integration
const reactExample = `
import React, { useState, useEffect, useRef } from 'react';

const WatchClassifier = () => {
    const [model, setModel] = useState(null);
    const [modelInfo, setModelInfo] = useState(null);
    const [isLoading, setIsLoading] = useState(false);
    const [result, setResult] = useState(null);
    const [error, setError] = useState(null);
    const fileInputRef = useRef(null);

    useEffect(() => {
        loadModel();
    }, []);

    const loadModel = async () => {
        try {
            setIsLoading(true);
            
            // Get model info
            const response = await fetch('/api/model-info');
            const info = await response.json();
            
            if (info.success) {
                setModelInfo(info);
                
                // Load TensorFlow.js model
                const modelURL = '/api/protected/model.json';
                const metadataURL = '/api/protected/metadata.json';
                
                const loadedModel = await window.tmImage.load(modelURL, metadataURL);
                setModel(loadedModel);
            } else {
                throw new Error('Failed to load model info');
            }
        } catch (err) {
            setError('Failed to load AI model: ' + err.message);
        } finally {
            setIsLoading(false);
        }
    };

    const classifyImage = async (file) => {
        if (!model) {
            setError('Model not loaded yet');
            return;
        }

        try {
            setIsLoading(true);
            setError(null);
            setResult(null);

            const img = new Image();
            const imageUrl = URL.createObjectURL(file);

            img.onload = async () => {
                try {
                    const predictions = await model.predict(img);
                    
                    const results = predictions.map(prediction => ({
                        label: prediction.className,
                        confidence: Math.round(prediction.probability * 100 * 100) / 100
                    }));
                    
                    results.sort((a, b) => b.confidence - a.confidence);
                    
                    setResult({
                        prediction: results[0].label,
                        confidence: results[0].confidence,
                        allResults: results
                    });
                    
                    URL.revokeObjectURL(imageUrl);
                } catch (err) {
                    setError('Classification failed: ' + err.message);
                    URL.revokeObjectURL(imageUrl);
                } finally {
                    setIsLoading(false);
                }
            };

            img.onerror = () => {
                setError('Failed to load image');
                URL.revokeObjectURL(imageUrl);
                setIsLoading(false);
            };

            img.src = imageUrl;
        } catch (err) {
            setError('Error processing image: ' + err.message);
            setIsLoading(false);
        }
    };

    const handleFileChange = (event) => {
        const file = event.target.files[0];
        if (file) {
            classifyImage(file);
        }
    };

    return (
        <div className="watch-classifier">
            <h2>TrustWatch - Watch Authenticity Verification</h2>
            
            {modelInfo && (
                <div className="model-info">
                    <p>Model: {modelInfo.modelName}</p>
                    <p>Categories: {modelInfo.labels.join(', ')}</p>
                </div>
            )}
            
            <input
                ref={fileInputRef}
                type="file"
                accept="image/*"
                onChange={handleFileChange}
                disabled={!model || isLoading}
            />
            
            {isLoading && <p>Processing...</p>}
            
            {error && <p style={{color: 'red'}}>{error}</p>}
            
            {result && (
                <div className="result">
                    <h3>Result: {result.prediction}</h3>
                    <p>Confidence: {result.confidence}%</p>
                    <div className="all-results">
                        {result.allResults.map((r, index) => (
                            <div key={index}>
                                {r.label}: {r.confidence}%
                            </div>
                        ))}
                    </div>
                </div>
            )}
        </div>
    );
};

export default WatchClassifier;
`;

// Example usage of the Node.js API
async function demonstrateAPI() {
    console.log('TrustWatch API Usage Examples');
    console.log('==============================');
    
    const api = new TrustWatchAPI();
    
    try {
        // Get model information
        console.log('\\n1. Getting model information...');
        const modelInfo = await api.getModelInfo();
        console.log('Model Info:', modelInfo);
        
        // Note: For image upload, you would need an actual image file
        // console.log('\\n2. Uploading image...');
        // const uploadResult = await api.uploadImage('./path/to/watch-image.jpg');
        // console.log('Upload Result:', uploadResult);
        
    } catch (error) {
        console.error('API Error:', error.message);
    }
}

// Export for use in other modules
module.exports = {
    TrustWatchAPI,
    frontendExample,
    reactExample,
    demonstrateAPI
};

// Run demonstration if this file is executed directly
if (require.main === module) {
    demonstrateAPI();
}
